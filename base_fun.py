import numpy as np
import networkx as nx
import random
from collections import Counter
from typing import List, Set, Tuple, Dict
import matplotlib.pyplot as plt


def gen_graph(filename: str) -> nx.Graph:
    """
    从文件加载图形数据并生成无向图
    
    Args:
        filename: 包含边列表的文件路径
        
    Returns:
        nx.Graph: 生成的无向图对象
        
    Raises:
        RuntimeError: 当图加载失败时
    """
    try:
        G = nx.Graph()  # 创建一个空的无向图
        edges_data = np.loadtxt(filename, skiprows=1, usecols=[0, 1])  # 读取边数据
        edges = [(int(u), int(v)) for u, v in edges_data]
        G.add_edges_from(edges)  # 将边添加到图中
        return G  # 返回生成的图
    except Exception as e:
        raise RuntimeError(f"加载图形错误: {e}")
    

# 影响力传播模拟 (IC模型)
def IC(g, seed, p, mc=1000):
    seed = set(seed)  # 转换为集合，避免重复元素
    influence = []
    neighbors_of = g.neighbors  # 绑定局部以减少属性查找
    rand = np.random.random
    for _ in range(mc):
        new_active, last_active = set(seed), set(seed)  # 使用集合来去重
        while new_active:
            new_ones = set()
            for node in new_active:
                for neighbor in neighbors_of(node):  # 直接迭代邻居，避免 list 转换
                    if rand() < p:
                        new_ones.add(neighbor)
            new_active = new_ones - last_active
            last_active.update(new_active)
        influence.append(len(last_active))  # 记录激活的总节点数
    return np.mean(influence)  # 返回平均影响力


# ---------------------------
# PRE 近似影响力估计
# ---------------------------

# def PRE(G: nx.Graph, S: Set[int], p: float, neighbors: Dict[int, List[int]], max_hop: int = 5) -> float:
#     """
#     PRE近似影响力估计（全节点递推）
#     使用原始的连乘计算更新
#     Args:
#         S: 种子节点集合
#         G: 网络图对象（NetworkX Graph）
#         p: 传播概率
#         neighbors: 每个节点的邻居节点信息，字典格式 {node: [neighbors]}
#         max_hop: 最大递推轮数
#     Returns:
#         float: 估计影响力值
#     """
#     S = set(S)
#     # 缓存节点列表到图对象，避免在频繁调用中重复构造
#     if not hasattr(G, "_node_list_cache"):
#         setattr(G, "_node_list_cache", list(G.nodes()))
#     nodes: List[int] = getattr(G, "_node_list_cache")

#     P: dict[int, float] = {v: 1.0 if v in S else 0.0 for v in nodes}

#     neighbors_get = neighbors.get
#     S_contains = S.__contains__
#     for _ in range(max_hop):
#         new_P: dict[int, float] = {}
#         # 先确保种子为 1
#         for sv in S:
#             new_P[sv] = 1.0
#         # 更新非种子
#         for v in nodes:
#             if S_contains(v):
#                 continue
#             parents = neighbors_get(v, [])
#             if not parents:
#                 new_P[v] = 0.0
#                 continue
#             prob_not = 1.0  # 连乘的“未被激活”概率
#             P_get = P.get
#             one_minus = 1.0 - p
#             for u in parents:
#                 Pu = P_get(u, 0.0)
#                 # factor = 1 - p * Pu，限制下界避免下溢
#                 factor = 1.0 - p * Pu
#                 if factor < 1e-20:
#                     factor = 1e-20
#                 prob_not *= factor
#             new_P[v] = 1.0 - prob_not
#         P = new_P

#     return float(sum(P.values()))  # 估计的总影响力（期望激活节点数）


def PRE(G: nx.Graph, S: Set[int], p: float, neighbors: Dict[int, List[int]], max_hop: int = 5) -> float:
    """
    PRE近似影响力估计（全节点递推）
    使用原始的连乘计算更新
    Args:
        S: 种子节点集合
        G: 网络图对象（NetworkX Graph）
        p: 传播概率
        neighbors: 每个节点的邻居节点信息，字典格式 {node: [neighbors]}
        max_hop: 最大递推轮数
    Returns:
        float: 估计影响力值
    """
    S = set(S)
    # 缓存节点列表到图对象，避免在频繁调用中重复构造
    if not hasattr(G, "_node_list_cache"):
        setattr(G, "_node_list_cache", list(G.nodes()))
    nodes: List[int] = getattr(G, "_node_list_cache")

    P: dict[int, float] = {v: 1.0 if v in S else 0.0 for v in nodes}

    neighbors_get = neighbors.get
    S_contains = S.__contains__
    for _ in range(max_hop):
        new_P: dict[int, float] = {}
        # 先确保种子为 1
        for sv in S:
            new_P[sv] = 1.0
        # 更新非种子
        for v in nodes:
            if S_contains(v):
                continue
            parents = neighbors_get(v, [])
            if not parents:
                new_P[v] = 0.0
                continue
            prob_not = 1.0  # 连乘的“未被激活”概率
            P_get = P.get
            one_minus = 1.0 - p
            for u in parents:
                Pu = P_get(u, 0.0)
                # factor = 1 - p * Pu，限制下界避免下溢
                factor = 1.0 - p * Pu
                if factor < 1e-20:
                    factor = 1e-20
                prob_not *= factor
            new_P[v] = 1.0 - prob_not
        P = new_P

    return float(sum(P.values()))  # 估计的总影响力（期望激活节点数）

def local_search(xi, G, p, k, neighbors=None, max_hop=5):
    """
    局部搜索算法，优化种子节点集合

    Args:
        xi: 当前种子集合
        G: NetworkX 图对象
        p: 传播概率
        k: 种子集合大小
        neighbors: 邻居字典，格式为 {node: [neighbors]}
        max_hop: PRE递推轮数

    Returns:
        list: 优化后的种子集合
    """
    # 如果没有提供neighbors字典，则生成一个
    if neighbors is None:
        neighbors = {v: list(G.neighbors(v)) for v in G.nodes()}

    # 确保xi是集合类型
    if not isinstance(xi, set):
        xi = set(xi)

    # 缓存当前种子集合的影响力值
    xi_fitness = PRE(G, xi, p, neighbors, max_hop)

    for x_ij in list(xi):  # 遍历当前种子节点的副本
        node_neighbors = list(G.neighbors(x_ij))  # 获取当前节点的邻居
        for neighbor in node_neighbors:  # 遍历邻居节点
            if neighbor not in xi:  # 如果邻居不在当前种子节点中
                # 尝试替换
                xi_new = xi.copy()  # 创建当前种子节点的副本
                xi_new.remove(x_ij)  # 从副本中移除当前节点
                xi_new.add(neighbor)  # 添加邻居节点

                # 只在影响力提高时才进行更新
                xi_new_fitness = PRE(G, xi_new, p, neighbors, max_hop)
                if xi_new_fitness > xi_fitness:
                    xi = xi_new  # 更新种子节点
                    xi_fitness = xi_new_fitness  # 更新当前种子集合的影响力值
                    break  # 退出邻居循环，尝试对下一个种子节点优化

    return list(xi)  # 返回优化后的种子节点列表